<template>
    <div p-20 class="container_box">
        <searchForm mb-20 v-model:formState="query" :formList="formList" @submit="getList" @reset="getReset"></searchForm>
        <div text-right>
            <a-button type="primary" mb-10 @click="handleEdit(false)">
                <PlusOutlined />
                新增评比活动
            </a-button>
            <a-button danger @click="deleteActivity" :disabled="!state.selectedRowKeys.length">删除</a-button>
        </div>

        <!-- 评价类型列表 -->
        <a-tabs v-if="!cloudState.codeId" v-model:evalTypeId="state.evalTypeId" @change="tabChange">
            <a-tab-pane :key="item.id" :tab="item.name" v-for="item in state.tabList"></a-tab-pane>
        </a-tabs>

        <ETable
            colSetting
            hash="evaluationManage_activity"
            :columns="columns"
            :loading="page.loading"
            :minH="360"
            :row-selection="{
                selectedRowKeys: state.selectedRowKeys,
                onChange: value => {
                    state.selectedRowKeys = value
                },
            }"
            :data-source="page.list"
            :total="page.total"
            @paginationChange="paginationChange"
            :current="page.pageNo"
            @change="handleTableChange"
        >
            <template #activityTime="{ record }">
                {{ `${record.startDate || ''} ~ ${record.endDate || ''}` }}
            </template>
            <template #status="{ record }">
                <a-badge :color="getStatusObj(record.status).color" :text="getStatusObj(record.status).text" />
            </template>
            <template #operationFlag="{ record }">
                <span>{{ getreviewObj(record.operationFlag) }}</span>
            </template>
            <template #enable="{ record }">
                <a-badge :color="getenableObj(record.enable).color" :text="getenableObj(record.enable).text" />
            </template>
            <template #names="{ record }">
                <span>{{ record.names.join('、') }}</span>
            </template>
            <template #operate="{ record }">
                <a class="btn-link-color" mr-5 @click="handleDetail(record)">详情</a>
                <!-- enable-0禁用 1-启用 -->
                <!-- edit 是否是管理员，是管理员才能编辑 -->
                <!-- status 0-未开始 1-进行中 2-已结束 -->
                <a class="btn-link-color" mr-5 v-if="record.edit && !record.enable" @click="handleEdit(true, record)">编辑</a>
                <a class="btn-link-color" v-if="record.enable" mr-5 @click="handleDisable(record)">禁用</a>
                <!-- 只有operationFlag等于1   status等于1 才可以进行立即评价  -->
                <a class="btn-link-color"  mr-5 @click="handleEvaluate(record)">
                    立即评价
                </a>
                <a class="btn-link-color" mr-5 v-if="!record.enable" @click="handleEnable(record)">启用</a>
                <a
                    class="btn-link-color"
                    v-if="record.isApprove && (record.status === 1 || record.status === 2)"
                    @click="handleAudit(record)"
                >
                    评价审核
                </a>
            </template>
        </ETable>

        <ActivityDetail ref="activityDetailRef" @childEvent="handleChildEvent" />
        <EvaluationModel :evalTypeId="state.evalTypeId" ref="evaluationModelRef" @close="close" />

        <!-- 这个是查看评价明细的抽屉 -->
        <EvaluationDetailed ref="evaluationDetailedRef" />

        <!-- 评价审核 -->
        <EvaluationAudit ref="evaluationAuditRef" />
    </div>
</template>
<script setup>
import ActivityDetail from './components/activityDetail.vue'
import EvaluationModel from './components/evaluationModel.vue'
import EvaluationDetailed from '../components/evaluationDetailed.vue'
import EvaluationAudit from '../components/evaluationAudit.vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { formList } from './configData.js'

const evaluationAuditRef = ref(null)
const cloudState = reactive({ ...getUrlParams() })

let { query, page, getList, reset, paginationChange } = useList('/cloud/evalActivity/page', { evalTypeId: cloudState.codeId || '' })

const state = reactive({
    selectedRowKeys: [],
    tabList: [],
    evalTypeId: '',
})

const handleAudit = item => {
    evaluationAuditRef.value.showModel(item)
}

// 进入这个页面就是直接获取评价类型
const getEvalTypeList = () => {
    http.get('/cloud/evalType/listBySchool').then(res => {
        state.tabList = res.data || []
        state.evalTypeId = res.data[0].id
        state.tabList.length && getList({ evalTypeId: state.evalTypeId })
    })
}

if (cloudState.codeId) {
    getList()
} else {
    getEvalTypeList()
}

const statusObj = {
    0: {
        text: '未开始',
        color: '#FDB500',
    },
    1: {
        text: '进行中',
        color: '#00C088',
    },
    2: {
        text: '已结束',
        color: '#595959',
    },
    default: {
        text: '-',
        color: '',
    }, // 默认值存储在 default 属性中
}

function getStatusObj(key) {
    return statusObj[key] || statusObj.default
}

const reviewObj = {
    0: '-',
    1: '未评价',
    2: '已评价',
    default: '-',
}

function getreviewObj(key) {
    return reviewObj[key] || reviewObj.default
}

const enableObj = {
    0: {
        text: '禁用',
        color: '#595959',
    },
    1: {
        text: '启用',
        color: '#00C088',
    },
    default: {
        text: '-',
        color: '',
    }, // 默认值存储在 default 属性中
}

function getenableObj(key) {
    return enableObj[key] || enableObj.default
}

const mockData = ref([])

// 新增评比
const addActivity = () => {
    console.log('新建')
}

// 删除评比
const deleteActivity = async () => {
    const flag = await yConfirm('删除', '确定要删除吗？')
    if (flag) {
        http.post('/cloud/evalActivity/delete', { ids: state.selectedRowKeys }).then(res => {
            YMessage.success('操作成功!')
            state.selectedRowKeys = []
            getList()
        })
    }
}

// tab切换
const tabChange = key => {
    state.evalTypeId = key
    page.pageNo = 1
    page.pageSize = 10
    getList({
        evalTypeId: state.evalTypeId,
    })
}

const orderType = {
    ascend: 'asc',
    descend: 'desc',
}

// table排序切换
const handleTableChange = (pag, filters, sorter) => {
    console.log('sorter :>> ', sorter)
    query.order = orderType[sorter.order]
    query.field = sorter.field
    getList()
}

const evaluationModelRef = ref(null)
// 编辑
const handleEdit = (isEdit, item) => {
    if (isEdit) {
        // 如果是编辑模式 需要把
        evaluationModelRef.value.showModel(isEdit, item)
    } else {
        evaluationModelRef.value.showModel(isEdit)
    }
}

// 触发编辑
const handleChildEvent = (isEdit, item) => {
    handleEdit(isEdit, item)
}

const activityDetailRef = ref(null)
const handleDetail = item => {
    activityDetailRef.value.showModel(item)
}

// 禁用
const handleDisable = async item => {
    const flag = await yConfirm('禁用', '确认要禁用吗？')
    if (flag) {
        http.post('/cloud/evalActivity/enable', { id: item.id, enable: 0 }).then(res => {
            getList()
        })
    }
}

// 启用
const handleEnable = async item => {
    const flag = await yConfirm('启用', '确认要启用吗？')
    if (flag) {
        http.post('/cloud/evalActivity/enable', { id: item.id, enable: 1 }).then(res => {
            getList()
        })
    }
}

const evaluationDetailedRef = ref(null)
// 立即评价
const handleEvaluate = item => {
    const params = {
        activityId: item.id,
        queryThisFrom: true,
    }
    evaluationDetailedRef.value.showDetailedModel(params, true, false)
}

const getReset = () => {
    reset({
        evalTypeId: state.evalTypeId || cloudState.codeId,
    })
}

// 抽屉关闭事件
const close = () => {
    getReset()
}

const columns = computed(() => {
    const arr = [
        { title: '活动名称', dataIndex: 'title', width: 200 },
        { title: '活动时间', dataIndex: 'activityTime', width: 200 },
        { title: '参与人数', dataIndex: 'partakePersonNum' },
        { title: '参与人员', dataIndex: 'names' },
        { title: '活动状态', dataIndex: 'enable' },
        { title: '创建人 ', dataIndex: 'createBy' },
        { title: '更新时间', dataIndex: 'updateTime' },
        { title: '操作', dataIndex: 'operate', width: 220 },
    ]

    if (cloudState.sysCode) {
        arr.splice(2, 0, { title: '评价状态', dataIndex: 'operationFlag' })
    } else {
        arr.splice(2, 0, { title: '评比状态', dataIndex: 'status' })
    }
    return arr
})
</script>
<style scoped lang="less">
.container_box {
    .drop {
        display: inline-block;
        width: 6px;
        height: 6px;
        border-radius: 50%;
    }
}
</style>
