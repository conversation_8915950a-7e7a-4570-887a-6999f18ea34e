# 多文件上传功能测试指南

## 测试环境
- 项目已启动在: http://localhost:9009/
- 修改的文件: `src/pages/yd-evaluation/evaluationManage/components/evaluationDetailed.vue`

## 测试步骤

### 1. 访问评价管理页面
1. 登录系统
2. 导航到 "评价管理" -> "评价详情" 页面
3. 找到包含文件上传功能的评价表格

### 2. 测试多选文件上传
1. **查看上传按钮文字**
   - 应该显示 "批量上传图片"、"批量上传视频" 或 "批量上传文件"
   
2. **测试多选图片**
   - 点击上传按钮
   - 在文件选择对话框中，按住 Ctrl/Cmd 键选择多个图片文件
   - 确认可以选择多个文件
   - 点击确定上传

3. **测试多选视频**
   - 点击上传按钮
   - 选择多个视频文件（如果支持视频上传）
   - 确认上传功能

4. **测试混合文件类型**
   - 如果同时支持图片和视频，尝试同时选择图片和视频文件
   - 验证系统是否正确处理不同类型的文件

### 3. 验证功能特性

#### 3.1 数量限制测试 ⭐⭐⭐ 核心测试（已修复）
- **图片限制测试**:
  - 先上传8张图片
  - 一次选择3张新图片
  - **预期结果**: 立即显示错误提示"图片总数不能超过9个！当前已有8个，本次选择3个，总计11个"
  - **验证**: 不会上传任何新图片，总数仍为8张

- **视频限制测试**:
  - 先上传1个视频
  - 选择1个新视频
  - **预期结果**: 立即显示错误提示"视频总数不能超过1个！当前已有1个，本次选择1个，总计2个"
  - **验证**: 不会上传新视频，总数仍为1个

- **边界测试**:
  - 上传9张图片（达到上限）
  - 选择1张新图片
  - **预期结果**: 立即阻止并提示
  - **验证**: 总数保持9张

#### 3.2 文件类型验证
- 尝试上传不支持的文件类型（如 .txt, .pdf）
- 应该显示相应的错误提示

#### 3.3 缩略图显示 ⭐ 重要变更
- 上传成功后，应该只显示第一个文件的缩略图
- 如果有多个文件，应该显示"+N"指示器（如 +2, +3）
- 缩略图大小为 100x100px
- 点击缩略图可以打开预览查看所有文件

#### 3.4 预览功能
- 点击缩略图应该打开预览模态框
- 预览模态框应该显示所有上传的文件
- 可以在预览中切换查看不同的文件

#### 3.5 删除功能
- 在预览模态框中应该可以删除文件
- 删除后缩略图和指示器应该更新
- 删除后的文件不应该在提交时包含

### 4. 错误处理测试

#### 4.1 网络错误
- 断开网络连接后尝试上传
- 应该显示上传失败的错误信息

#### 4.2 部分文件失败
- 如果部分文件上传成功，部分失败
- 应该显示具体哪些文件失败了

#### 4.3 文件大小限制
- 上传超大文件测试系统响应

### 5. 兼容性测试

#### 5.1 单文件上传兼容性
- 选择单个文件上传，确保功能正常
- 验证与原有单文件上传逻辑的兼容性

#### 5.2 现有数据兼容性
- 如果已有上传的文件，新上传的文件应该追加到现有文件列表
- 不应该覆盖已有的文件

## 预期结果

### 成功指标
✅ 可以同时选择多个文件  
✅ 上传按钮文字显示"批量上传"  
✅ 所有选中的文件都能成功上传  
✅ 缩略图正确显示所有文件  
✅ 预览功能正常工作  
✅ 删除功能正常工作  
✅ 数量限制正确执行  
✅ 文件类型验证正确执行  
✅ 错误信息准确显示  

### 性能指标
✅ 多文件上传速度合理  
✅ 界面响应流畅  
✅ 内存使用正常  

## 常见问题排查

### 问题1: 无法选择多个文件
- 检查 `multiple` 属性是否正确添加到 `a-upload` 组件
- 确认浏览器支持多文件选择

### 问题2: 上传失败
- 检查网络连接
- 查看浏览器控制台错误信息
- 确认服务器端接口正常

### 问题3: 缩略图显示异常
- 检查CSS样式是否正确应用
- 确认图片URL有效

### 问题4: 预览功能异常
- 检查 MediaPreviewModal 组件是否正确接收数据
- 确认传递的路径格式正确

## 回归测试
在完成多文件上传测试后，还需要验证：
1. 原有的单文件上传功能是否仍然正常
2. 其他页面的文件上传功能是否受到影响
3. 评价提交功能是否正常工作
4. 数据保存和读取是否正确
