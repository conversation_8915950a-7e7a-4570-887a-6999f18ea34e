# 文件上传多选功能修改总结

## 修改内容

### 1. 启用多选文件上传
- 在 `a-upload` 组件中添加了 `multiple` 属性，支持多选文件
- 修改了 `before-upload` 处理函数，从 `handleFileUpload` 改为 `handleMultipleFileUpload`

### 2. 重写文件上传处理逻辑
- 创建了新的 `handleMultipleFileUpload` 函数来处理多文件上传
- 支持同时上传多个图片和视频文件
- 保持原有的文件类型验证和数量限制逻辑
- 添加了批量上传的进度提示和错误处理

### 3. 更新缩略图显示
- 修改缩略图显示区域，从只显示第一个文件改为显示所有文件
- 使用网格布局展示多个缩略图
- 每个缩略图都可以点击打开预览模态框

### 4. 优化用户界面
- 更新上传按钮文字，添加"批量"前缀
- 调整缩略图尺寸和布局，支持多文件展示
- 保持原有的预览和删除功能

### 5. 功能特性
- **多文件选择**: 用户可以一次选择多个文件进行上传
- **类型验证**: 仍然支持图片和视频文件类型验证
- **数量限制**: 保持图片最多9个，视频最多1个的限制
- **批量上传**: 所有选中的文件会并行上传
- **错误处理**: 如果部分文件上传失败，会显示具体的错误信息
- **进度反馈**: 上传成功后会显示成功上传的文件数量

## 主要修改的文件

### `src/pages/yd-evaluation/evaluationManage/components/evaluationDetailed.vue`

#### 模板修改:
```vue
<!-- 添加 multiple 属性 -->
<a-upload
    :file-list="[]"
    :before-upload="files => handleMultipleFileUpload(files, record)"
    :accept="getAcceptTypes(record.evalScoreTypeList)"
    :show-upload-list="false"
    multiple
>
    <a-button size="small" type="primary" ghost>
        <template #icon>
            <CloudUploadOutlined />
        </template>
        {{ getUploadButtonText(record.evalScoreTypeList) }}
    </a-button>
</a-upload>

<!-- 更新缩略图显示 -->
<div class="thumbnail-container" style="margin-top: 8px">
    <div class="thumbnail-list">
        <!-- 显示所有图片缩略图 -->
        <div
            v-for="(imageUrl, index) in getImageList(record.imgPaths)"
            :key="`image-${index}`"
            class="thumbnail-item"
            @click="openPreviewModal(record)"
        >
            <img :src="imageUrl" alt="图片缩略图" />
            <div class="thumbnail-overlay">
                <EyeOutlined />
            </div>
        </div>
        
        <!-- 显示所有视频缩略图 -->
        <div
            v-for="(videoUrl, index) in getVideoList(record.videoPaths)"
            :key="`video-${index}`"
            class="thumbnail-item video-thumbnail"
            @click="openPreviewModal(record)"
        >
            <video :src="videoUrl" muted></video>
            <div class="thumbnail-overlay">
                <PlayCircleOutlined />
            </div>
        </div>
    </div>
</div>
```

#### 脚本修改:
- 新增 `handleMultipleFileUpload` 函数处理多文件上传
- 更新 `getUploadButtonText` 函数，添加"批量"前缀
- 移除不再使用的 `getFirstMediaFile` 和 `getTotalMediaCount` 函数
- 优化缩略图显示逻辑

#### 样式修改:
- 更新 `.thumbnail-list` 使用网格布局
- 调整缩略图尺寸为 80x80px
- 限制缩略图容器最大宽度，避免占用过多空间

## 使用说明

1. **选择文件**: 点击上传按钮，可以同时选择多个图片或视频文件
2. **类型限制**: 根据评分方式配置，只能上传允许的文件类型
3. **数量限制**: 图片总数不超过9个，视频总数不超过1个
4. **预览功能**: 点击任意缩略图可以打开预览模态框查看所有文件
5. **删除功能**: 在预览模态框中可以删除不需要的文件

## 兼容性

- 保持与现有 MediaPreviewModal 组件的完全兼容
- 保持原有的数据结构和API接口不变
- 向后兼容单文件上传的使用场景
